{"configVersion": 2, "packages": [{"name": "_fe_analyzer_shared", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/_fe_analyzer_shared-82.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "analyzer", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/analyzer-7.4.5", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "animations", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/animations-2.0.11", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "args", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/args-2.7.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "async", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "boolean_selector", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "build", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/build-2.4.2", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "built_collection", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/built_collection-5.1.1", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "built_value", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/built_value-8.9.5", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "characters", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "clock", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "code_builder", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/code_builder-4.10.1", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "collection", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "convert", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/convert-3.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "crypto", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/crypto-3.0.6", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "cupertino_icons", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "dart_style", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/dart_style-3.1.0", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "equatable", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/equatable-2.0.7", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "fake_async", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "ffi", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "file", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "fixnum", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/fixnum-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "fl_chart", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/fl_chart-0.70.2", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "flutter", "rootUri": "file:///home/<USER>/.cache/flutter_sdk/packages/flutter", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_lints", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "flutter_riverpod", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_riverpod-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_screenutil", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/flutter_screenutil-5.9.3", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "flutter_test", "rootUri": "file:///home/<USER>/.cache/flutter_sdk/packages/flutter_test", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "flutter_web_plugins", "rootUri": "file:///home/<USER>/.cache/flutter_sdk/packages/flutter_web_plugins", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "glob", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/glob-2.1.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "http", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "http_parser", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "intl", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/intl-0.20.2", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "leak_tracker", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_flutter_testing", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "leak_tracker_testing", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "lints", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "logging", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/logging-1.3.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "matcher", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "material_color_utilities", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "meta", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "<PERSON><PERSON>", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/mockito-5.4.6", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "package_config", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/package_config-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "path_provider_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "path_provider_foundation", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "path_provider_linux", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1", "packageUri": "lib/", "languageVersion": "2.19"}, {"name": "path_provider_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "path_provider_windows", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "platform", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "plugin_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8", "packageUri": "lib/", "languageVersion": "3.0"}, {"name": "pub_semver", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/pub_semver-2.2.0", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "riverpod", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/riverpod-2.6.1", "packageUri": "lib/", "languageVersion": "2.17"}, {"name": "shared_preferences", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "shared_preferences_android", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "shared_preferences_foundation", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_linux", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "shared_preferences_platform_interface", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1", "packageUri": "lib/", "languageVersion": "3.2"}, {"name": "shared_preferences_web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "shared_preferences_windows", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "sky_engine", "rootUri": "file:///home/<USER>/.cache/flutter_sdk/bin/cache/pkg/sky_engine", "packageUri": "lib/", "languageVersion": "3.7"}, {"name": "source_gen", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/source_gen-2.0.0", "packageUri": "lib/", "languageVersion": "3.6"}, {"name": "source_span", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "stack_trace", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "state_notifier", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/state_notifier-1.0.0", "packageUri": "lib/", "languageVersion": "2.12"}, {"name": "stream_channel", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "string_scanner", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "term_glyph", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "test_api", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "typed_data", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0", "packageUri": "lib/", "languageVersion": "3.5"}, {"name": "vector_math", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4", "packageUri": "lib/", "languageVersion": "2.14"}, {"name": "vm_service", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "watcher", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/watcher-1.1.1", "packageUri": "lib/", "languageVersion": "3.1"}, {"name": "web", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/web_socket-1.0.1", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "web_socket_channel", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/web_socket_channel-3.0.3", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "xdg_directories", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0", "packageUri": "lib/", "languageVersion": "3.3"}, {"name": "yaml", "rootUri": "file:///home/<USER>/.pub-cache/hosted/pub.dev/yaml-3.1.3", "packageUri": "lib/", "languageVersion": "3.4"}, {"name": "time_tracker_gui", "rootUri": "../", "packageUri": "lib/", "languageVersion": "3.7"}], "generated": "2025-05-26T20:11:50.627942Z", "generator": "pub", "generatorVersion": "3.7.2", "flutterRoot": "file:///home/<USER>/.cache/flutter_sdk", "flutterVersion": "3.29.3", "pubCache": "file:///home/<USER>/.pub-cache"}