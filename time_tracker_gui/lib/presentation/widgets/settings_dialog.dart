import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../providers/settings_provider.dart';
import '../providers/theme_provider.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';

class SettingsDialog extends ConsumerStatefulWidget {
  const SettingsDialog({super.key});

  @override
  ConsumerState<SettingsDialog> createState() => _SettingsDialogState();
}

class _SettingsDialogState extends ConsumerState<SettingsDialog> {
  late TextEditingController _backendUrlController;
  late TextEditingController _refreshIntervalController;
  bool _hasUnsavedChanges = false;

  @override
  void initState() {
    super.initState();
    final settings = ref.read(settingsProvider);
    _backendUrlController = TextEditingController(text: settings.backendUrl);
    _refreshIntervalController = TextEditingController(text: settings.refreshInterval.toString());
  }

  @override
  void dispose() {
    _backendUrlController.dispose();
    _refreshIntervalController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(settingsProvider);
    final theme = Theme.of(context);

    return AlertDialog(
      title: Row(
        children: [
          Icon(
            Icons.settings,
            color: theme.colorScheme.primary,
          ),
          SizedBox(width: UIConstants.spacingS.w),
          const Text('Settings'),
        ],
      ),
      content: SizedBox(
        width: 400.w,
        child: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSectionTitle('Backend Configuration'),
              _buildBackendUrlField(),
              SizedBox(height: UIConstants.spacingM.h),
              
              _buildSectionTitle('Appearance'),
              _buildThemeSelector(),
              SizedBox(height: UIConstants.spacingM.h),
              
              _buildSectionTitle('Behavior'),
              _buildNotificationToggle(settings),
              _buildAutoStartToggle(settings),
              _buildRefreshIntervalField(),
              SizedBox(height: UIConstants.spacingM.h),
              
              _buildSectionTitle('Actions'),
              _buildActionButtons(),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        if (_hasUnsavedChanges)
          ElevatedButton(
            onPressed: _saveSettings,
            child: const Text('Save'),
          ),
        ElevatedButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Close'),
        ),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: UIConstants.spacingS.h),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleSmall?.copyWith(
          fontWeight: FontWeight.w600,
          color: Theme.of(context).colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildBackendUrlField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: _backendUrlController,
          decoration: const InputDecoration(
            labelText: 'Backend URL',
            hintText: 'http://localhost:8080',
            prefixIcon: Icon(Icons.link),
          ),
          onChanged: (_) => setState(() => _hasUnsavedChanges = true),
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Text(
          'The URL of your time tracker backend service',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildThemeSelector() {
    final currentTheme = ref.watch(themeModeProvider);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Theme',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w500,
          ),
        ),
        SizedBox(height: UIConstants.spacingS.h),
        Row(
          children: [
            Expanded(
              child: RadioListTile<ThemeMode>(
                title: const Text('Light'),
                value: ThemeMode.light,
                groupValue: currentTheme,
                onChanged: (value) {
                  if (value != null) {
                    ref.read(themeModeProvider.notifier).setThemeMode(value);
                  }
                },
                dense: true,
              ),
            ),
            Expanded(
              child: RadioListTile<ThemeMode>(
                title: const Text('Dark'),
                value: ThemeMode.dark,
                groupValue: currentTheme,
                onChanged: (value) {
                  if (value != null) {
                    ref.read(themeModeProvider.notifier).setThemeMode(value);
                  }
                },
                dense: true,
              ),
            ),
            Expanded(
              child: RadioListTile<ThemeMode>(
                title: const Text('System'),
                value: ThemeMode.system,
                groupValue: currentTheme,
                onChanged: (value) {
                  if (value != null) {
                    ref.read(themeModeProvider.notifier).setThemeMode(value);
                  }
                },
                dense: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildNotificationToggle(AppSettings settings) {
    return SwitchListTile(
      title: const Text('Enable Notifications'),
      subtitle: const Text('Show notifications for tracking events'),
      value: settings.enableNotifications,
      onChanged: (value) {
        ref.read(settingsProvider.notifier).updateNotifications(value);
      },
    );
  }

  Widget _buildAutoStartToggle(AppSettings settings) {
    return SwitchListTile(
      title: const Text('Auto-start Tracking'),
      subtitle: const Text('Automatically start tracking when app launches'),
      value: settings.enableAutoStart,
      onChanged: (value) {
        ref.read(settingsProvider.notifier).updateAutoStart(value);
      },
    );
  }

  Widget _buildRefreshIntervalField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextField(
          controller: _refreshIntervalController,
          decoration: const InputDecoration(
            labelText: 'Refresh Interval (seconds)',
            hintText: '5',
            prefixIcon: Icon(Icons.refresh),
          ),
          keyboardType: TextInputType.number,
          onChanged: (_) => setState(() => _hasUnsavedChanges = true),
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Text(
          'How often to refresh data from the backend',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Column(
      children: [
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _testConnection,
            icon: const Icon(Icons.wifi_protected_setup),
            label: const Text('Test Connection'),
          ),
        ),
        SizedBox(height: UIConstants.spacingS.h),
        SizedBox(
          width: double.infinity,
          child: OutlinedButton.icon(
            onPressed: _resetToDefaults,
            icon: const Icon(Icons.restore),
            label: const Text('Reset to Defaults'),
            style: OutlinedButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
              side: BorderSide(color: Theme.of(context).colorScheme.error),
            ),
          ),
        ),
      ],
    );
  }

  void _saveSettings() async {
    final url = _backendUrlController.text.trim();
    final intervalText = _refreshIntervalController.text.trim();
    
    if (url.isNotEmpty && !ref.read(settingsProvider.notifier).isValidUrl(url)) {
      _showErrorSnackBar('Please enter a valid URL');
      return;
    }
    
    final interval = int.tryParse(intervalText);
    if (interval == null || interval < 1 || interval > 300) {
      _showErrorSnackBar('Refresh interval must be between 1 and 300 seconds');
      return;
    }
    
    await ref.read(settingsProvider.notifier).updateBackendUrl(url);
    await ref.read(settingsProvider.notifier).updateRefreshInterval(interval);
    
    setState(() => _hasUnsavedChanges = false);
    _showSuccessSnackBar('Settings saved successfully');
  }

  void _testConnection() async {
    final url = _backendUrlController.text.trim();
    if (url.isEmpty || !ref.read(settingsProvider.notifier).isValidUrl(url)) {
      _showErrorSnackBar('Please enter a valid URL first');
      return;
    }
    
    // TODO: Implement actual connection test
    _showSuccessSnackBar('Connection test - Feature coming soon');
  }

  void _resetToDefaults() async {
    final confirmed = await _showConfirmationDialog(
      'Reset Settings',
      'Are you sure you want to reset all settings to their default values?',
    );
    
    if (confirmed) {
      await ref.read(settingsProvider.notifier).resetToDefaults();
      final settings = ref.read(settingsProvider);
      _backendUrlController.text = settings.backendUrl;
      _refreshIntervalController.text = settings.refreshInterval.toString();
      setState(() => _hasUnsavedChanges = false);
      _showSuccessSnackBar('Settings reset to defaults');
    }
  }

  Future<bool> _showConfirmationDialog(String title, String content) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
    return result ?? false;
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).colorScheme.error,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Theme.of(context).successColor,
      ),
    );
  }
}
