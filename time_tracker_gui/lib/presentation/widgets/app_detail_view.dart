import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../data/models/app_model.dart';
import '../providers/app_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import 'dashboard_card.dart';

class AppDetailView extends ConsumerStatefulWidget {
  final AppModel app;

  const AppDetailView({
    super.key,
    required this.app,
  });

  @override
  ConsumerState<AppDetailView> createState() => _AppDetailViewState();
}

class _AppDetailViewState extends ConsumerState<AppDetailView> {
  String _selectedTimeRange = 'Last 7 days';
  final List<String> _timeRanges = [
    'Today',
    'Last 7 days',
    'Last 30 days',
    'All time',
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.app.name ?? 'App Details'),
        actions: [
          IconButton(
            icon: const Icon(Icons.edit),
            onPressed: () => _showEditDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.delete),
            onPressed: () => _showDeleteConfirmation(context),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(UIConstants.spacingM.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // App Overview Card
            DashboardCard(
              title: 'Overview',
              child: _buildOverviewSection(),
            ),
            SizedBox(height: UIConstants.spacingM.h),

            // Time Range Selector
            _buildTimeRangeSelector(),
            SizedBox(height: UIConstants.spacingM.h),

            // Usage Statistics
            DashboardCard(
              title: 'Usage Statistics',
              child: _buildUsageStatistics(),
            ),
            SizedBox(height: UIConstants.spacingM.h),

            // Recent Sessions
            DashboardCard(
              title: 'Recent Sessions',
              child: _buildRecentSessions(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOverviewSection() {
    return Column(
      children: [
        Row(
          children: [
            _buildAppIcon(),
            SizedBox(width: UIConstants.spacingM.w),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    widget.app.name ?? 'Unknown App',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  if (widget.app.productName != null) ...[
                    SizedBox(height: UIConstants.spacingXS.h),
                    Text(
                      widget.app.productName!,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
        SizedBox(height: UIConstants.spacingM.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Total Duration',
                _formatDuration(widget.app.duration ?? 0),
                Icons.timer,
                Theme.of(context).colorScheme.primary,
              ),
            ),
            SizedBox(width: UIConstants.spacingS.w),
            Expanded(
              child: _buildStatCard(
                'Launches',
                '${widget.app.launches ?? 0}',
                Icons.launch,
                Theme.of(context).colorScheme.secondary,
              ),
            ),
          ],
        ),
        SizedBox(height: UIConstants.spacingS.h),
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                'Longest Session',
                _formatDuration(widget.app.longestSession ?? 0),
                Icons.trending_up,
                Theme.of(context).colorScheme.tertiary,
              ),
            ),
            SizedBox(width: UIConstants.spacingS.w),
            Expanded(
              child: _buildStatCard(
                'Last Used',
                widget.app.longestSessionOn?.toString().split(' ')[0] ?? 'Never',
                Icons.schedule,
                Theme.of(context).colorScheme.outline,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildAppIcon() {
    final theme = Theme.of(context);
    return Container(
      width: 60.w,
      height: 60.w,
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.2),
          width: 2,
        ),
      ),
      child: Icon(
        Icons.apps,
        color: theme.colorScheme.primary,
        size: UIConstants.iconL,
      ),
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.all(UIConstants.spacingS.w),
      decoration: BoxDecoration(
        color: isDark
            ? AppTheme.darkSurface.withOpacity(0.5)
            : AppTheme.neutralGray50,
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
        border: Border.all(
          color: isDark
              ? AppTheme.neutralGray700
              : AppTheme.neutralGray200,
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: UIConstants.iconM,
          ),
          SizedBox(height: UIConstants.spacingXS.h),
          Text(
            value,
            style: theme.textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.w700,
              color: color,
            ),
          ),
          Text(
            label,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildTimeRangeSelector() {
    return Row(
      children: [
        Text(
          'Time Range:',
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(width: UIConstants.spacingM.w),
        Expanded(
          child: DropdownButton<String>(
            value: _selectedTimeRange,
            isExpanded: true,
            items: _timeRanges.map((range) => DropdownMenuItem(
              value: range,
              child: Text(range),
            )).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedTimeRange = value;
                });
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildUsageStatistics() {
    return Column(
      children: [
        Text(
          'Detailed usage statistics for $_selectedTimeRange will be displayed here.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        SizedBox(height: UIConstants.spacingM.h),
        // Placeholder for charts
        Container(
          height: 200.h,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.3),
            borderRadius: BorderRadius.circular(UIConstants.radiusM),
            border: Border.all(
              color: Theme.of(context).colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.bar_chart,
                  size: UIConstants.iconXL,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                SizedBox(height: UIConstants.spacingS.h),
                Text(
                  'Usage Chart',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRecentSessions() {
    return Column(
      children: [
        Text(
          'Recent sessions and time entries will be displayed here.',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        SizedBox(height: UIConstants.spacingM.h),
        // Placeholder for session list
        ...List.generate(3, (index) => _buildSessionItem(index)),
      ],
    );
  }

  Widget _buildSessionItem(int index) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.only(bottom: UIConstants.spacingS.h),
      padding: EdgeInsets.all(UIConstants.spacingS.w),
      decoration: BoxDecoration(
        color: isDark
            ? AppTheme.darkSurface.withOpacity(0.3)
            : AppTheme.neutralGray50,
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
        border: Border.all(
          color: isDark
              ? AppTheme.neutralGray700
              : AppTheme.neutralGray200,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.circle,
            size: UIConstants.iconXS,
            color: theme.colorScheme.primary,
          ),
          SizedBox(width: UIConstants.spacingS.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Session ${index + 1}',
                  style: theme.textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${DateTime.now().subtract(Duration(days: index)).toString().split(' ')[0]}',
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${(index + 1) * 15}m',
            style: theme.textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.primary,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}m';
    }
  }

  void _showEditDialog(BuildContext context) {
    // TODO: Implement edit functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Edit functionality coming soon')),
    );
  }

  void _showDeleteConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete App'),
        content: Text('Are you sure you want to delete "${widget.app.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(appsProvider.notifier).deleteApp(widget.app.id);
              Navigator.of(context).pop(); // Close dialog
              Navigator.of(context).pop(); // Close detail view
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}
