import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';

class DashboardCard extends StatelessWidget {
  final String title;
  final Widget child;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final Color? backgroundColor;
  final Color? borderColor;
  final Widget? trailing;
  final bool isCompact;

  const DashboardCard({
    super.key,
    required this.title,
    required this.child,
    this.onTap,
    this.padding,
    this.backgroundColor,
    this.borderColor,
    this.trailing,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      constraints: BoxConstraints(
        minHeight: isCompact ? UIConstants.cardMinHeight : UIConstants.cardMinHeight + 20,
      ),
      decoration: BoxDecoration(
        color: backgroundColor ?? theme.cardColor,
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        border: Border.all(
          color: borderColor ?? (isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray200),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(UIConstants.radiusL),
          child: Padding(
            padding: padding ?? EdgeInsets.all(isCompact ? UIConstants.spacingS.w : UIConstants.spacingM.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        title,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                    ),
                    if (trailing != null) trailing!,
                  ],
                ),
                SizedBox(height: UIConstants.spacingS.h),
                child,
              ],
            ),
          ),
        ),
      ),
    );
  }
}
