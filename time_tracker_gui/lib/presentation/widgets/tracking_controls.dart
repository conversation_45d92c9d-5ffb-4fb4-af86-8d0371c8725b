import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../data/models/app_model.dart';
import '../providers/app_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';

class TrackingControls extends ConsumerWidget {
  final TrackingStatus status;

  const TrackingControls({
    super.key,
    required this.status,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Column(
      children: [
        _buildStatusIndicator(context),
        SizedBox(height: UIConstants.spacingL.h),
        _buildCurrentApp(context),
        SizedBox(height: UIConstants.spacingL.h),
        _buildSessionInfo(context),
        SizedBox(height: UIConstants.spacingXL.h),
        _buildControlButtons(context, ref),
      ],
    );
  }

  Widget _buildStatusIndicator(BuildContext context) {
    Color statusColor;
    String statusText;
    IconData statusIcon;

    if (status.isTracking && !status.isPaused) {
      statusColor = AppTheme.successColor;
      statusText = 'Tracking Active';
      statusIcon = Icons.play_circle_filled;
    } else if (status.isPaused) {
      statusColor = AppTheme.warningColor;
      statusText = 'Tracking Paused';
      statusIcon = Icons.pause_circle_filled;
    } else {
      statusColor = AppTheme.neutralGray500;
      statusText = 'Tracking Stopped';
      statusIcon = Icons.stop_circle;
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingL.w,
        vertical: UIConstants.spacingM.h,
      ),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.08),
        borderRadius: BorderRadius.circular(UIConstants.radiusXL),
        border: Border.all(
          color: statusColor.withOpacity(0.2),
          width: 1.5,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: EdgeInsets.all(UIConstants.spacingXS),
            decoration: BoxDecoration(
              color: statusColor.withOpacity(0.15),
              shape: BoxShape.circle,
            ),
            child: Icon(
              statusIcon,
              color: statusColor,
              size: UIConstants.iconS.sp,
            ),
          ),
          SizedBox(width: UIConstants.spacingM.w),
          Text(
            statusText,
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              color: statusColor,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentApp(BuildContext context) {
    if (status.currentApp == null) {
      return Text(
        'No app being tracked',
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
          color: Theme.of(context).colorScheme.onSurfaceVariant,
        ),
      );
    }

    return Column(
      children: [
        Text(
          'Currently Tracking',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Container(
          padding: EdgeInsets.symmetric(
            horizontal: UIConstants.spacingM.w,
            vertical: UIConstants.spacingS.h,
          ),
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(UIConstants.radiusM),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.apps,
                color: Theme.of(context).colorScheme.onPrimaryContainer,
                size: 18.sp,
              ),
              SizedBox(width: UIConstants.spacingS.w),
              Text(
                status.currentApp!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSessionInfo(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildInfoItem(
          context,
          'Session Time',
          _formatDuration(status.currentSessionDuration),
          Icons.timer,
        ),
        _buildInfoItem(
          context,
          'Started',
          status.sessionStartTime != null
              ? _formatTime(status.sessionStartTime!)
              : 'N/A',
          Icons.schedule,
        ),
      ],
    );
  }

  Widget _buildInfoItem(BuildContext context, String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: Theme.of(context).colorScheme.primary,
          size: 20.sp,
        ),
        SizedBox(height: UIConstants.spacingXS.h),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildControlButtons(BuildContext context, WidgetRef ref) {
    return Wrap(
      spacing: UIConstants.spacingM.w,
      runSpacing: UIConstants.spacingS.h,
      alignment: WrapAlignment.center,
      children: [
        if (!status.isTracking)
          _buildActionButton(
            context: context,
            onPressed: () => ref.read(trackingStatusProvider.notifier).startTracking(),
            icon: Icons.play_arrow,
            label: 'Start Tracking',
            backgroundColor: AppTheme.successColor,
            foregroundColor: Colors.white,
          ),

        if (status.isTracking && !status.isPaused)
          _buildActionButton(
            context: context,
            onPressed: () => ref.read(trackingStatusProvider.notifier).pauseTracking(),
            icon: Icons.pause,
            label: 'Pause',
            backgroundColor: AppTheme.warningColor,
            foregroundColor: Colors.white,
          ),

        if (status.isTracking && status.isPaused)
          _buildActionButton(
            context: context,
            onPressed: () => ref.read(trackingStatusProvider.notifier).resumeTracking(),
            icon: Icons.play_arrow,
            label: 'Resume',
            backgroundColor: AppTheme.successColor,
            foregroundColor: Colors.white,
          ),

        if (status.isTracking)
          _buildActionButton(
            context: context,
            onPressed: () => ref.read(trackingStatusProvider.notifier).stopTracking(),
            icon: Icons.stop,
            label: 'Stop',
            backgroundColor: AppTheme.errorColor,
            foregroundColor: Colors.white,
          ),
      ],
    );
  }

  Widget _buildActionButton({
    required BuildContext context,
    required VoidCallback onPressed,
    required IconData icon,
    required String label,
    required Color backgroundColor,
    required Color foregroundColor,
  }) {
    return Container(
      height: UIConstants.buttonHeightL,
      constraints: BoxConstraints(
        minWidth: 120.w,
      ),
      child: ElevatedButton.icon(
        onPressed: onPressed,
        icon: Icon(icon, size: UIConstants.iconS),
        label: Text(
          label,
          style: TextStyle(
            fontSize: AppTheme.fontSizeS,
            fontWeight: FontWeight.w600,
          ),
        ),
        style: ElevatedButton.styleFrom(
          backgroundColor: backgroundColor,
          foregroundColor: foregroundColor,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(UIConstants.radiusM),
          ),
          padding: EdgeInsets.symmetric(
            horizontal: UIConstants.spacingL,
            vertical: UIConstants.spacingM,
          ),
        ),
      ),
    );
  }

  String _formatDuration(int seconds) {
    final hours = seconds ~/ 3600;
    final minutes = (seconds % 3600) ~/ 60;
    final secs = seconds % 60;

    if (hours > 0) {
      return '${hours.toString().padLeft(2, '0')}:${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${secs.toString().padLeft(2, '0')}';
    }
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
