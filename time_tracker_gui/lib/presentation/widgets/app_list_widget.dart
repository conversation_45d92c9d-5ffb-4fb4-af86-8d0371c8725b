import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../data/models/app_model.dart';
import '../providers/app_providers.dart';
import '../../core/constants/ui_constants.dart';
import '../../app/theme/app_theme.dart';
import 'app_detail_view.dart';

class AppListWidget extends ConsumerWidget {
  final List<AppModel> apps;
  final bool isCompact;

  const AppListWidget({
    super.key,
    required this.apps,
    this.isCompact = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (apps.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.apps,
              size: 64.sp,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
            SizedBox(height: UIConstants.spacingM.h),
            Text(
              'No apps tracked yet',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
            SizedBox(height: UIConstants.spacingS.h),
            Text(
              'Add an app to start tracking',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: isCompact,
      physics: isCompact ? const NeverScrollableScrollPhysics() : null,
      itemCount: apps.length,
      itemBuilder: (context, index) {
        final app = apps[index];
        return AppListItem(
          app: app,
          isCompact: isCompact,
          onDelete: () => _showDeleteConfirmation(context, ref, app),
        );
      },
    );
  }

  void _showDeleteConfirmation(BuildContext context, WidgetRef ref, AppModel app) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete App'),
        content: Text('Are you sure you want to delete "${app.name}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              ref.read(appsProvider.notifier).deleteApp(app.id);
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.error,
              foregroundColor: Colors.white,
            ),
            child: const Text('Delete'),
          ),
        ],
      ),
    );
  }
}

class AppListItem extends StatelessWidget {
  final AppModel app;
  final bool isCompact;
  final VoidCallback? onDelete;

  const AppListItem({
    super.key,
    required this.app,
    this.isCompact = false,
    this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      margin: EdgeInsets.symmetric(
        horizontal: isCompact ? 0 : UIConstants.spacingXS.w,
        vertical: UIConstants.spacingXS.h,
      ),
      decoration: BoxDecoration(
        color: theme.cardColor,
        borderRadius: BorderRadius.circular(UIConstants.radiusL),
        border: Border.all(
          color: isDark ? AppTheme.neutralGray700 : AppTheme.neutralGray200,
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => _showAppDetails(context),
          borderRadius: BorderRadius.circular(UIConstants.radiusL),
          child: Padding(
            padding: EdgeInsets.all(UIConstants.spacingS.w),
            child: Row(
              children: [
                _buildAppIcon(context),
                SizedBox(width: UIConstants.spacingM.w),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        app.name ?? 'Unknown App',
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: theme.colorScheme.onSurface,
                        ),
                      ),
                      if (!isCompact && app.productName != null) ...[
                        SizedBox(height: UIConstants.spacingXS.h),
                        Text(
                          app.productName!,
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                      if (!isCompact) ...[
                        SizedBox(height: UIConstants.spacingS.h),
                        Row(
                          children: [
                            _buildStatChip(
                              context,
                              'Duration',
                              _formatDuration(app.duration ?? 0),
                              Icons.timer,
                            ),
                            SizedBox(width: UIConstants.spacingS.w),
                            _buildStatChip(
                              context,
                              'Launches',
                              '${app.launches ?? 0}',
                              Icons.launch,
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
                if (!isCompact) ...[
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        _formatDuration(app.duration ?? 0),
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w700,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                      if (onDelete != null) ...[
                        SizedBox(height: UIConstants.spacingXS.h),
                        IconButton(
                          icon: Icon(
                            Icons.delete_outline,
                            size: UIConstants.iconS,
                          ),
                          onPressed: onDelete,
                          color: theme.colorScheme.error,
                          style: IconButton.styleFrom(
                            backgroundColor: theme.colorScheme.errorContainer.withOpacity(0.1),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(UIConstants.radiusS),
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAppIcon(BuildContext context) {
    final theme = Theme.of(context);
    return Container(
      width: UIConstants.iconXL,
      height: UIConstants.iconXL,
      decoration: BoxDecoration(
        color: theme.colorScheme.primaryContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(UIConstants.radiusM),
        border: Border.all(
          color: theme.colorScheme.primary.withOpacity(0.2),
          width: 1,
        ),
      ),
      child: Icon(
        Icons.apps,
        color: theme.colorScheme.primary,
        size: UIConstants.iconM,
      ),
    );
  }

  Widget _buildStatChip(BuildContext context, String label, String value, IconData icon) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: UIConstants.spacingM.w,
        vertical: UIConstants.spacingS.h,
      ),
      decoration: BoxDecoration(
        color: isDark
          ? AppTheme.neutralGray800.withOpacity(0.5)
          : AppTheme.neutralGray100,
        borderRadius: BorderRadius.circular(UIConstants.radiusXL),
        border: Border.all(
          color: isDark
            ? AppTheme.neutralGray700
            : AppTheme.neutralGray200,
          width: 1,
        ),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: UIConstants.iconXS,
            color: theme.colorScheme.onSurfaceVariant,
          ),
          SizedBox(width: UIConstants.spacingXS.w),
          Text(
            value,
            style: theme.textTheme.bodySmall?.copyWith(
              color: theme.colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w600,
              fontSize: AppTheme.fontSizeXS,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(int minutes) {
    if (minutes < 60) {
      return '${minutes}m';
    } else {
      final hours = minutes ~/ 60;
      final remainingMinutes = minutes % 60;
      return '${hours}h ${remainingMinutes}m';
    }
  }

  void _showAppDetails(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AppDetailView(app: app),
      ),
    );
  }
}
