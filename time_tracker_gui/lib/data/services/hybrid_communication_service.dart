import 'dart:async';
import '../models/app_model.dart';
import 'api_service.dart';
import 'websocket_service.dart';
import '../../core/constants/api_constants.dart';

/// Hybrid communication service that uses WebSocket for real-time updates
/// with HTTP polling as fallback for reliability
class HybridCommunicationService {
  final ApiService _apiService;
  final WebSocketService _webSocketService;

  Timer? _fallbackTimer;
  bool _useWebSocket = true;
  bool _isConnected = false;

  final StreamController<TrackingStatus> _trackingStatusController = StreamController.broadcast();
  final StreamController<AppModel> _appUpdateController = StreamController.broadcast();
  final StreamController<Map<String, dynamic>> _sessionUpdateController = StreamController.broadcast();

  Stream<TrackingStatus> get trackingStatusStream => _trackingStatusController.stream;
  Stream<AppModel> get appUpdateStream => _appUpdateController.stream;
  Stream<Map<String, dynamic>> get sessionUpdateStream => _sessionUpdateController.stream;

  bool get isConnected => _isConnected;
  bool get isUsingWebSocket => _useWebSocket && _webSocketService.isConnected;

  HybridCommunicationService({
    required ApiService apiService,
    required WebSocketService webSocketService,
  }) : _apiService = apiService,
       _webSocketService = webSocketService;

  Future<void> connect() async {
    print('Starting hybrid communication service...');

    // Try WebSocket first
    try {
      await _webSocketService.connect();
      if (_webSocketService.isConnected) {
        _useWebSocket = true;
        _isConnected = true;
        _setupWebSocketListeners();
        print('Using WebSocket for real-time communication');
        return;
      }
    } catch (e) {
      print('WebSocket connection failed, falling back to HTTP polling: $e');
    }

    // Fallback to HTTP polling
    _useWebSocket = false;
    _isConnected = true;
    _startHttpPolling();
    print('Using HTTP polling for communication');
  }

  void _setupWebSocketListeners() {
    // Listen to WebSocket streams and forward to our streams
    _webSocketService.trackingStatusStream.listen(
      (status) => _trackingStatusController.add(status),
      onError: (error) {
        print('WebSocket tracking status error: $error');
        _fallbackToHttpPolling();
      },
    );

    _webSocketService.appUpdateStream.listen(
      (app) => _appUpdateController.add(app),
      onError: (error) {
        print('WebSocket app update error: $error');
        _fallbackToHttpPolling();
      },
    );

    _webSocketService.sessionUpdateStream.listen(
      (session) => _sessionUpdateController.add(session),
      onError: (error) {
        print('WebSocket session update error: $error');
        _fallbackToHttpPolling();
      },
    );
  }

  void _fallbackToHttpPolling() {
    if (_useWebSocket) {
      print('WebSocket connection lost, falling back to HTTP polling');
      _useWebSocket = false;
      _startHttpPolling();
    }
  }

  void _startHttpPolling() {
    _fallbackTimer?.cancel();
    _fallbackTimer = Timer.periodic(
      const Duration(seconds: 1), // Fast polling for better responsiveness
      (timer) => _pollTrackingStatus(),
    );
  }

  Future<void> _pollTrackingStatus() async {
    try {
      final status = await _apiService.getTrackingStatus();
      _trackingStatusController.add(status);
    } catch (e) {
      print('HTTP polling error: $e');
      // Don't mark as disconnected for HTTP errors, just log them
    }
  }

  // Forward API calls
  Future<List<AppModel>> getAllApps() => _apiService.getAllApps();

  Future<AppModel?> getAppByName(String name) => _apiService.getAppByName(name);

  Future<void> insertApp(String name) => _apiService.insertApp(name);

  Future<void> deleteApp(int appId) => _apiService.deleteApp(appId);

  Future<TrackingStatus> getTrackingStatus() => _apiService.getTrackingStatus();

  Future<void> startTracking() async {
    // Send command via HTTP for immediate response
    await _apiService.sendTrackingCommand('start');

    // If using WebSocket, also send via WebSocket for faster response
    if (isUsingWebSocket) {
      _webSocketService.sendTrackingCommand('start');
    }
  }

  Future<void> stopTracking() async {
    await _apiService.sendTrackingCommand('stop');
    if (isUsingWebSocket) {
      _webSocketService.sendTrackingCommand('stop');
    }
  }

  Future<void> pauseTracking() async {
    await _apiService.sendTrackingCommand('pause');
    if (isUsingWebSocket) {
      _webSocketService.sendTrackingCommand('pause');
    }
  }

  Future<void> resumeTracking() async {
    await _apiService.sendTrackingCommand('resume');
    if (isUsingWebSocket) {
      _webSocketService.sendTrackingCommand('resume');
    }
  }

  Future<List<TimelineModel>> getTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) => _apiService.getTimeline(
    startDate: startDate,
    endDate: endDate,
    appId: appId,
  );

  Future<List<AppStatistics>> getStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) => _apiService.getStatistics(
    startDate: startDate,
    endDate: endDate,
  );

  void disconnect() {
    _fallbackTimer?.cancel();
    _webSocketService.disconnect();
    _isConnected = false;
  }

  void dispose() {
    disconnect();
    _trackingStatusController.close();
    _appUpdateController.close();
    _sessionUpdateController.close();
    _apiService.dispose();
    _webSocketService.dispose();
  }
}
