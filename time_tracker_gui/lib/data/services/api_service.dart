import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/app_model.dart';
import '../../core/constants/api_constants.dart';

class ApiService {
  final http.Client _client;
  final String _baseUrl;

  ApiService({
    http.Client? client,
    String? baseUrl,
  }) : _client = client ?? http.Client(),
       _baseUrl = baseUrl ?? ApiConstants.baseUrl;

  Future<List<AppModel>> getAllApps() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl${ApiConstants.apps}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        // Handle Scala backend response format: {"success": true, "data": [...]}
        if (responseData is Map<String, dynamic> && responseData['success'] == true) {
          final List<dynamic> jsonList = responseData['data'] ?? [];
          return jsonList.map((json) => AppModel.fromJson(json)).toList();
        } else {
          throw ApiException('Invalid response format');
        }
      } else {
        throw ApiException('Failed to fetch apps: ${response.statusCode}');
      }
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  Future<AppModel?> getAppByName(String name) async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl${ApiConstants.apps}/$name'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData is Map<String, dynamic> && responseData['success'] == true) {
          return AppModel.fromJson(responseData['data']);
        } else {
          throw ApiException('Invalid response format');
        }
      } else if (response.statusCode == 404) {
        return null;
      } else {
        throw ApiException('Failed to fetch app: ${response.statusCode}');
      }
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  Future<void> insertApp(String name) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl${ApiConstants.apps}'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'name': name}),
      );

      if (response.statusCode != 201) {
        throw ApiException('Failed to insert app: ${response.statusCode}');
      }
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  Future<void> deleteApp(int appId) async {
    try {
      final response = await _client.delete(
        Uri.parse('$_baseUrl${ApiConstants.apps}/$appId'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode != 200) {
        throw ApiException('Failed to delete app: ${response.statusCode}');
      }
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  Future<List<TimelineModel>> getTimeline({
    DateTime? startDate,
    DateTime? endDate,
    int? appId,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String().split('T')[0];
      if (appId != null) queryParams['app_id'] = appId.toString();

      final uri = Uri.parse('$_baseUrl${ApiConstants.timeline}').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      final response = await _client.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData is Map<String, dynamic> && responseData['success'] == true) {
          final List<dynamic> jsonList = responseData['data'] ?? [];
          return jsonList.map((json) => TimelineModel.fromJson(json)).toList();
        } else {
          throw ApiException('Invalid response format');
        }
      } else {
        throw ApiException('Failed to fetch timeline: ${response.statusCode}');
      }
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  Future<TrackingStatus> getTrackingStatus() async {
    try {
      final response = await _client.get(
        Uri.parse('$_baseUrl${ApiConstants.tracking}/status'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData is Map<String, dynamic> && responseData['success'] == true) {
          return TrackingStatus.fromJson(responseData['data']);
        } else {
          throw ApiException('Invalid response format');
        }
      } else {
        throw ApiException('Failed to fetch tracking status: ${response.statusCode}');
      }
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  Future<void> sendTrackingCommand(String command) async {
    try {
      final response = await _client.post(
        Uri.parse('$_baseUrl${ApiConstants.tracking}/command'),
        headers: {'Content-Type': 'application/json'},
        body: json.encode({'command': command}),
      );

      if (response.statusCode != 200) {
        throw ApiException('Failed to send command: ${response.statusCode}');
      }
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  Future<List<AppStatistics>> getStatistics({
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      final queryParams = <String, String>{};
      if (startDate != null) queryParams['start_date'] = startDate.toIso8601String().split('T')[0];
      if (endDate != null) queryParams['end_date'] = endDate.toIso8601String().split('T')[0];

      final uri = Uri.parse('$_baseUrl${ApiConstants.statistics}').replace(
        queryParameters: queryParams.isNotEmpty ? queryParams : null,
      );

      final response = await _client.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        final responseData = json.decode(response.body);
        if (responseData is Map<String, dynamic> && responseData['success'] == true) {
          final List<dynamic> jsonList = responseData['data'] ?? [];
          return jsonList.map((json) => AppStatistics.fromJson(json)).toList();
        } else {
          throw ApiException('Invalid response format');
        }
      } else {
        throw ApiException('Failed to fetch statistics: ${response.statusCode}');
      }
    } catch (e) {
      throw ApiException('Network error: $e');
    }
  }

  void dispose() {
    _client.close();
  }
}

class ApiException implements Exception {
  final String message;
  ApiException(this.message);

  @override
  String toString() => 'ApiException: $message';
}
