//> using dep org.tpolecat::skunk-core::0.6.4
//> using dep org.slf4j:slf4j-nop:2.0.17
//> using dep com.github.alexarchambault::case-app::2.1.0-M30
//> using dep com.lihaoyi::os-lib::0.11.4
//> using dep org.http4s::http4s-ember-server::0.23.30
//> using dep org.http4s::http4s-ember-client::0.23.30
//> using dep org.http4s::http4s-circe::0.23.30
//> using dep org.http4s::http4s-dsl::0.23.30
//> using dep io.circe::circe-core::0.14.13
//> using dep io.circe::circe-generic::0.14.13
//> using dep io.circe::circe-parser::0.14.13
//> using dep org.http4s::http4s-jdk-http-client::0.10.0
//> using dep com.comcast::ip4s-core::3.7.0


import cats.effect._
import cats.syntax.all._
import fs2.{Stream, concurrent}
import fs2.concurrent.SignallingRef
import skunk._
import skunk.codec.all._
import natchez.Trace.Implicits.noop

import caseapp._
import scala.concurrent.duration._
import java.time.LocalDate
import scala.util.Try

import org.http4s._
import org.http4s.dsl.io._
import org.http4s.ember.server._
import org.http4s.server.Router
import org.http4s.server.middleware._
import org.http4s.circe.CirceEntityCodec._

// Circe imports - being explicit to avoid conflicts
import _root_.io.circe.{Encoder, Decoder}
import _root_.io.circe.generic.auto._
import _root_.io.circe.syntax._
import _root_.io.circe.parser._

// IP4S import for ipv4 interpolator
import com.comcast.ip4s._

// Data Models
case class App(
    id: Int,
    name: Option[String],
    productName: Option[String],
    duration: Option[Int],
    launches: Option[Int],
    longestSession: Option[Int],
    longestSessionOn: Option[LocalDate]
)

case class Timeline(
    id: Int,
    date: Option[LocalDate],
    duration: Option[Int],
    appId: Int
)

case class TrackingStatus(
    isTracking: Boolean,
    isPaused: Boolean,
    currentApp: Option[String],
    currentSessionDuration: Int,
    sessionStartTime: Option[String]
)

case class AppStatistics(
    app: App,
    totalDuration: Int,
    todayDuration: Int,
    weekDuration: Int,
    monthDuration: Int,
    averageSessionLength: Double,
    recentSessions: List[Timeline]
)

case class CommandRequest(command: String)
case class InsertAppRequest(name: String)
case class ApiResponse[T](success: Boolean, data: Option[T] = None, message: Option[String] = None)

// WebSocket Message Types
case class WebSocketMessage(`type`: String, payload: _root_.io.circe.Json)
case class WebSocketCommand(`type`: String, payload: Map[String, String])

object WebSocketEvents {
  val TrackingStatusUpdate = "tracking_status_update"
  val AppUpdate = "app_update"
  val SessionUpdate = "session_update"
  val Command = "command"
}

// Configuration
@AppName("time_tracker_enhanced")
@AppVersion("2.0.0")
case class Config(
    @HelpMessage("Display data for process and return")
    display: Option[String] = None,
    @HelpMessage("Display data for all processes")
    displayAll: Boolean = false,
    @HelpMessage("Insert process if it is not already tracked")
    insert: Option[String] = None,
    @HelpMessage("Print debug output")
    debug: Boolean = false,
    @HelpMessage("Pause/Unpause tracking")
    pause: Boolean = false,
    @HelpMessage("Start HTTP API server")
    server: Boolean = false,
    @HelpMessage("HTTP server port")
    port: Int = 8080
)

// Database Operations (same as before)
class DatabaseOps(session: Session[IO]) {
  import skunk.{Query => SkunkQuery, Command => SkunkCommand, Void, Codec => SkunkCodec}
  import skunk.implicits._
  import skunk.~

  // Codecs
  private val appCodec: SkunkCodec[App] =
    (int4 ~ varchar(255).opt ~ varchar(255).opt ~ int4.opt ~ int4.opt ~ int4.opt ~ date.opt)
      .imap {
        case id ~ name ~ productName ~ duration ~ launches ~ longestSession ~ longestSessionOn =>
          App(id, name, productName, duration, launches, longestSession, longestSessionOn)
      }(app => app.id ~ app.name ~ app.productName ~ app.duration ~ app.launches ~ app.longestSession ~ app.longestSessionOn)

  private val timelineCodec: SkunkCodec[Timeline] =
    (int4 ~ date.opt ~ int4.opt ~ int4)
      .imap {
        case id ~ date ~ duration ~ appId =>
          Timeline(id, date, duration, appId)
      }(timeline => timeline.id ~ timeline.date ~ timeline.duration ~ timeline.appId)

  // Queries
  private val selectAppByName: SkunkQuery[String, App] =
    sql"SELECT id, name, product_name, duration, launches, longest_session, longest_session_on FROM apps WHERE name = $varchar"
      .query(appCodec)

  private val selectAllApps: SkunkQuery[Void, App] =
    sql"SELECT id, name, product_name, duration, launches, longest_session, longest_session_on FROM apps ORDER BY id"
      .query(appCodec)

  private val selectAppById: SkunkQuery[Int, App] =
    sql"SELECT id, name, product_name, duration, launches, longest_session, longest_session_on FROM apps WHERE id = $int4"
      .query(appCodec)

  private val selectTimelineByDateRange: SkunkQuery[(LocalDate, LocalDate), Timeline] =
    sql"SELECT id, date, duration, app_id FROM timeline WHERE date BETWEEN $date AND $date ORDER BY date DESC"
      .query(timelineCodec)

  private val selectTimelineByApp: SkunkQuery[Int, Timeline] =
    sql"SELECT id, date, duration, app_id FROM timeline WHERE app_id = $int4 ORDER BY date DESC"
      .query(timelineCodec)

  // Commands
  private val insertApp: SkunkCommand[String] =
    sql"INSERT INTO apps (name) VALUES ($varchar)"
      .command

  private val deleteAppById: SkunkCommand[Int] =
    sql"DELETE FROM apps WHERE id = $int4"
      .command

  private val updateAppDuration: SkunkCommand[(Int, Int)] =
    sql"UPDATE apps SET duration = $int4 WHERE id = $int4"
      .command

  private val updateAppLaunches: SkunkCommand[(Int, Int)] =
    sql"UPDATE apps SET launches = $int4 WHERE id = $int4"
      .command

  private val updateLongestSession: SkunkCommand[(Int, LocalDate, Int)] =
    sql"UPDATE apps SET longest_session = $int4, longest_session_on = $date WHERE id = $int4"
      .command

  private val upsertTimeline: SkunkCommand[(String, LocalDate)] =
    sql"CALL upsert_timeline($varchar, $date)"
      .command

  // Operations
  def getAppByName(name: String): IO[Option[App]] =
    session.prepare(selectAppByName).flatMap(_.option(name))

  def getAppById(id: Int): IO[Option[App]] =
    session.prepare(selectAppById).flatMap(_.option(id))

  def getAllApps: IO[List[App]] =
    session.prepare(selectAllApps).flatMap(_.stream(Void, 1024).compile.toList)

  def insertNewApp(name: String): IO[Unit] =
    session.prepare(insertApp).flatMap(_.execute(name)).void

  def deleteApp(id: Int): IO[Unit] =
    session.prepare(deleteAppById).flatMap(_.execute(id)).void

  def updateDuration(appId: Int, duration: Int): IO[Unit] =
    session.prepare(updateAppDuration).flatMap(_.execute((duration, appId))).void

  def updateLaunches(appId: Int, launches: Int): IO[Unit] =
    session.prepare(updateAppLaunches).flatMap(_.execute((launches, appId))).void

  def updateLongestSessionRecord(appId: Int, sessionDuration: Int): IO[Unit] =
    session.prepare(updateLongestSession).flatMap(_.execute((sessionDuration, LocalDate.now(), appId))).void

  def updateTimeline(processName: String): IO[Unit] =
    session.prepare(upsertTimeline).flatMap(_.execute((processName, LocalDate.now()))).void

  def getTimelineByDateRange(startDate: LocalDate, endDate: LocalDate): IO[List[Timeline]] =
    session.prepare(selectTimelineByDateRange).flatMap(_.stream((startDate, endDate), 1024).compile.toList)

  def getTimelineByApp(appId: Int): IO[List[Timeline]] =
    session.prepare(selectTimelineByApp).flatMap(_.stream(appId, 1024).compile.toList)
}

// WebSocket Service for real-time communication
class WebSocketService {
  import fs2.concurrent.Topic
  import org.http4s.websocket.WebSocketFrame
  import org.http4s.websocket.WebSocketFrame._

  private val maxQueued = 1000

  def createWebSocketTopic: IO[Topic[IO, WebSocketMessage]] =
    Topic[IO, WebSocketMessage]

  def broadcastMessage(topic: Topic[IO, WebSocketMessage], message: WebSocketMessage): IO[Unit] =
    topic.publish1(message).void

  def broadcastTrackingStatus(topic: Topic[IO, WebSocketMessage], status: TrackingStatus): IO[Unit] = {
    val message = WebSocketMessage(WebSocketEvents.TrackingStatusUpdate, status.asJson)
    broadcastMessage(topic, message)
  }

  def broadcastAppUpdate(topic: Topic[IO, WebSocketMessage], app: App): IO[Unit] = {
    val message = WebSocketMessage(WebSocketEvents.AppUpdate, app.asJson)
    broadcastMessage(topic, message)
  }

  def webSocketRoutes(topic: Topic[IO, WebSocketMessage]): HttpRoutes[IO] = {
    import org.http4s.dsl.io._
    import org.http4s.server.websocket._

    HttpRoutes.of[IO] {
      case GET -> Root / "ws" =>
        val toClient: Stream[IO, WebSocketFrame] = topic.subscribe(maxQueued)
          .map(msg => Text(msg.asJson.noSpaces))

        val fromClient: fs2.Pipe[IO, WebSocketFrame, Unit] = _.evalMap {
          case Text(text, _) =>
            _root_.io.circe.parser.decode[WebSocketCommand](text) match {
              case Right(cmd) =>
                IO.println(s"Received WebSocket command: ${cmd.`type`}")
              case Left(error) =>
                IO.println(s"Failed to parse WebSocket message: $error")
            }
          case _ => IO.unit
        }

        WebSocketBuilder[IO].build(toClient, fromClient)
    }
  }
}

// Enhanced Communication Service with HTTP API and WebSocket support
class EnhancedCommunicationService(
    pauseSignal: SignallingRef[IO, Boolean],
    db: DatabaseOps,
    debug: Boolean,
    trackingStatusRef: Ref[IO, TrackingStatus],
    webSocketService: WebSocketService,
    webSocketTopic: fs2.concurrent.Topic[IO, WebSocketMessage]
) {

  private val commandFile = "/tmp/time-tracker-commands"

  // Legacy file-based communication (kept for backward compatibility)
  def checkForCommands: IO[Unit] = {
    IO {
      Try {
        if (os.exists(os.Path(commandFile))) {
          val content = os.read(os.Path(commandFile)).trim
          os.remove(os.Path(commandFile))
          Some(content)
        } else None
      }.getOrElse(None)
    }.flatMap {
      case Some("pause") => togglePause
      case Some("start") => startTracking
      case Some("stop") => stopTracking
      case Some("resume") => resumeTracking
      case Some(cmd) if cmd.startsWith("insert:") =>
        val processName = cmd.substring("insert:".length)
        insertProcessCommand(processName)
      case _ => IO.unit
    }
  }

  // New HTTP-based commands with WebSocket broadcasting
  def startTracking: IO[Unit] = {
    for {
      _ <- pauseSignal.set(false)
      _ <- trackingStatusRef.update(_.copy(isTracking = true, isPaused = false, sessionStartTime = Some(java.time.Instant.now().toString)))
      status <- trackingStatusRef.get
      _ <- webSocketService.broadcastTrackingStatus(webSocketTopic, status)
      _ <- if (debug) IO.println("Tracking started") else IO.unit
    } yield ()
  }

  def stopTracking: IO[Unit] = {
    for {
      _ <- pauseSignal.set(false)
      _ <- trackingStatusRef.update(_.copy(isTracking = false, isPaused = false, currentApp = None, currentSessionDuration = 0, sessionStartTime = None))
      status <- trackingStatusRef.get
      _ <- webSocketService.broadcastTrackingStatus(webSocketTopic, status)
      _ <- if (debug) IO.println("Tracking stopped") else IO.unit
    } yield ()
  }

  def pauseTracking: IO[Unit] = {
    for {
      _ <- pauseSignal.set(true)
      _ <- trackingStatusRef.update(_.copy(isPaused = true))
      status <- trackingStatusRef.get
      _ <- webSocketService.broadcastTrackingStatus(webSocketTopic, status)
      _ <- if (debug) IO.println("Tracking paused") else IO.unit
    } yield ()
  }

  def resumeTracking: IO[Unit] = {
    for {
      _ <- pauseSignal.set(false)
      _ <- trackingStatusRef.update(_.copy(isPaused = false))
      status <- trackingStatusRef.get
      _ <- webSocketService.broadcastTrackingStatus(webSocketTopic, status)
      _ <- if (debug) IO.println("Tracking resumed") else IO.unit
    } yield ()
  }

  def togglePause: IO[Unit] = {
    for {
      current <- pauseSignal.get
      _ <- pauseSignal.set(!current)
      _ <- trackingStatusRef.update(status => status.copy(isPaused = !status.isPaused))
      status <- trackingStatusRef.get
      _ <- webSocketService.broadcastTrackingStatus(webSocketTopic, status)
      _ <- if (debug) IO.println("Toggled pause state") else IO.unit
    } yield ()
  }

  // Start real-time status broadcaster for session duration updates
  def startStatusBroadcaster: IO[Unit] = {
    Stream.awakeEvery[IO](1.second)
      .evalMap { _ =>
        trackingStatusRef.get.flatMap { status =>
          if (status.isTracking && !status.isPaused) {
            val updatedStatus = status.copy(currentSessionDuration = status.currentSessionDuration + 1)
            trackingStatusRef.set(updatedStatus) *>
            webSocketService.broadcastTrackingStatus(webSocketTopic, updatedStatus)
          } else IO.unit
        }
      }
      .compile
      .drain
  }

  def getCurrentStatus: IO[TrackingStatus] = trackingStatusRef.get

  private def insertProcessCommand(processName: String): IO[Unit] = {
    db.getAppByName(processName).flatMap {
      case Some(_) =>
        if (debug) IO.println(s"App '$processName' already exists") else IO.unit
      case None =>
        db.insertNewApp(processName) *>
        (if (debug) IO.println(s"App '$processName' has been inserted") else IO.unit)
    }
  }

  def startCommandListener: IO[Unit] = {
    Stream.awakeEvery[IO](5.second)
      .evalMap(_ => checkForCommands)
      .compile
      .drain
  }
}

// HTTP API Routes with WebSocket support
class ApiRoutes(
    db: DatabaseOps,
    communicationService: EnhancedCommunicationService,
    webSocketService: WebSocketService,
    webSocketTopic: fs2.concurrent.Topic[IO, WebSocketMessage],
    debug: Boolean
) {

  private def corsMiddleware = CORS.policy.withAllowOriginAll.withAllowCredentials(false)

  val routes: HttpRoutes[IO] = corsMiddleware(Router(
    "/api" -> HttpRoutes.of[IO] {

      // Apps endpoints
      case GET -> Root / "apps" =>
        db.getAllApps.flatMap { apps =>
          Ok(ApiResponse(success = true, data = Some(apps)).asJson)
        }.handleErrorWith { error =>
          if (debug) IO.println(s"Error getting apps: $error") *>
          InternalServerError(ApiResponse[List[App]](success = false, message = Some(error.getMessage)).asJson)
          else InternalServerError(ApiResponse[List[App]](success = false, message = Some("Internal server error")).asJson)
        }

      case GET -> Root / "apps" / name =>
        db.getAppByName(name).flatMap {
          case Some(app) => Ok(ApiResponse(success = true, data = Some(app)).asJson)
          case None => NotFound(ApiResponse[App](success = false, message = Some("App not found")).asJson)
        }.handleErrorWith { error =>
          if (debug) IO.println(s"Error getting app: $error") *>
          InternalServerError(ApiResponse[App](success = false, message = Some(error.getMessage)).asJson)
          else InternalServerError(ApiResponse[App](success = false, message = Some("Internal server error")).asJson)
        }

      case req @ POST -> Root / "apps" =>
        req.as[InsertAppRequest].flatMap { request =>
          db.insertNewApp(request.name) *>
          Created(ApiResponse[String](success = true, message = Some("App created successfully")).asJson)
        }.handleErrorWith { error =>
          if (debug) IO.println(s"Error creating app: $error") *>
          BadRequest(ApiResponse[String](success = false, message = Some(error.getMessage)).asJson)
          else BadRequest(ApiResponse[String](success = false, message = Some("Invalid request")).asJson)
        }

      case DELETE -> Root / "apps" / IntVar(id) =>
        db.deleteApp(id) *>
        Ok(ApiResponse[String](success = true, message = Some("App deleted successfully")).asJson)
        .handleErrorWith { error =>
          if (debug) IO.println(s"Error deleting app: $error") *>
          InternalServerError(ApiResponse[String](success = false, message = Some(error.getMessage)).asJson)
          else InternalServerError(ApiResponse[String](success = false, message = Some("Internal server error")).asJson)
        }

      // Tracking endpoints
      case GET -> Root / "tracking" / "status" =>
        communicationService.getCurrentStatus.flatMap { status =>
          Ok(ApiResponse(success = true, data = Some(status)).asJson)
        }

      case req @ POST -> Root / "tracking" / "command" =>
        req.as[CommandRequest].flatMap { request =>
          val action = request.command.toLowerCase match {
            case "start" => communicationService.startTracking
            case "stop" => communicationService.stopTracking
            case "pause" => communicationService.pauseTracking
            case "resume" => communicationService.resumeTracking
            case _ => IO.raiseError(new IllegalArgumentException("Invalid command"))
          }
          action *> Ok(ApiResponse[String](success = true, message = Some("Command executed successfully")).asJson)
        }.handleErrorWith { error =>
          BadRequest(ApiResponse[String](success = false, message = Some(error.getMessage)).asJson)
        }

      // Timeline endpoints
      case req @ GET -> Root / "timeline" =>
        val startDateParam = req.params.get("start_date")
        val endDateParam = req.params.get("end_date")
        val appIdParam = req.params.get("app_id")

        val timelineIO = (startDateParam, endDateParam, appIdParam) match {
          case (Some(start), Some(end), _) =>
            Try {
              val startDate = LocalDate.parse(start)
              val endDate = LocalDate.parse(end)
              db.getTimelineByDateRange(startDate, endDate)
            }.getOrElse(IO.raiseError(new IllegalArgumentException("Invalid date format")))
          case (_, _, Some(appId)) =>
            Try(appId.toInt).map(db.getTimelineByApp).getOrElse(IO.raiseError(new IllegalArgumentException("Invalid app ID")))
          case _ =>
            // Default: last 30 days
            val endDate = LocalDate.now()
            val startDate = endDate.minusDays(30)
            db.getTimelineByDateRange(startDate, endDate)
        }

        timelineIO.flatMap { timeline =>
          Ok(ApiResponse(success = true, data = Some(timeline)).asJson)
        }.handleErrorWith { error =>
          BadRequest(ApiResponse[List[Timeline]](success = false, message = Some(error.getMessage)).asJson)
        }

      // Statistics endpoints
      case GET -> Root / "statistics" =>
        for {
          apps <- db.getAllApps
          statistics <- apps.traverse { app =>
            for {
              timeline <- db.getTimelineByApp(app.id)
              todayTimeline = timeline.filter(_.date.contains(LocalDate.now()))
              weekTimeline = timeline.filter(_.date.exists(_.isAfter(LocalDate.now().minusDays(7))))
              monthTimeline = timeline.filter(_.date.exists(_.isAfter(LocalDate.now().minusDays(30))))
            } yield AppStatistics(
              app = app,
              totalDuration = app.duration.getOrElse(0),
              todayDuration = todayTimeline.map(_.duration.getOrElse(0)).sum,
              weekDuration = weekTimeline.map(_.duration.getOrElse(0)).sum,
              monthDuration = monthTimeline.map(_.duration.getOrElse(0)).sum,
              averageSessionLength = if (timeline.nonEmpty) timeline.map(_.duration.getOrElse(0)).sum.toDouble / timeline.length else 0.0,
              recentSessions = timeline.take(10)
            )
          }
          response <- Ok(ApiResponse(success = true, data = Some(statistics)).asJson)
        } yield response
    },
    "/" -> webSocketService.webSocketRoutes(webSocketTopic)
  ))
}

// Process Utilities (same as before)
object ProcessUtils {
  def isProcessRunning(processName: String): IO[Boolean] =
    IO {
      Try {
        os.proc("pgrep", "-f", processName).call(check = false).exitCode == 0
      }.getOrElse(false)
    }

  def sendNotification(message: String): IO[Unit] =
    IO {
      Try {
        os.proc("notify-send", "time_tracker", message).call(check = false)
      }.recover { case _ => () }
    }.void
}

// Enhanced Process Monitor with status updates and WebSocket broadcasting
class ProcessMonitor(
    db: DatabaseOps,
    pauseSignal: SignallingRef[IO, Boolean],
    debug: Boolean,
    trackingStatusRef: Ref[IO, TrackingStatus],
    webSocketService: WebSocketService,
    webSocketTopic: fs2.concurrent.Topic[IO, WebSocketMessage]
) {

  case class MonitorState(
    currentDuration: Int = 0,
    totalDuration: Int = 0,
    launched: Boolean = false
  )

  def monitorProcess(processName: String, app: App): IO[Unit] = {
    val initialState = MonitorState(totalDuration = app.duration.getOrElse(0))

    ProcessUtils.sendNotification(s"Started tracking \"$processName\"") *>
    trackingStatusRef.update(_.copy(currentApp = Some(processName))) *>
    trackingStatusRef.get.flatMap(status => webSocketService.broadcastTrackingStatus(webSocketTopic, status)) *>
    Stream.awakeEvery[IO](1.minute)
      .evalMap { _ =>
        pauseSignal.get.flatMap { isPaused =>
          if (isPaused) {
            if (debug) IO.println(s"Monitoring paused for $processName") else IO.unit *>
            IO.pure(None)
          } else {
            ProcessUtils.isProcessRunning(processName).map(Some(_))
          }
        }
      }
      .collect { case Some(isRunning) => isRunning }
      .takeWhile(identity)
      .evalMapAccumulate(initialState) { (state, _) =>
        val newState = state.copy(
          currentDuration = state.currentDuration + 1,
          totalDuration = state.totalDuration + 1
        )

        val updateLaunches = if (!state.launched) {
          val newLaunches = app.launches.getOrElse(0) + 1
          db.updateLaunches(app.id, newLaunches) *>
          (if (debug) IO.println(s"Updated launches for $processName to $newLaunches") else IO.unit) *>
          IO.pure(newState.copy(launched = true))
        } else IO.pure(newState)

        updateLaunches.flatMap { updatedState =>
          // Update database and tracking status every minute
          db.updateDuration(app.id, updatedState.totalDuration) *>
          db.updateTimeline(processName) *>
          trackingStatusRef.update(_.copy(currentSessionDuration = updatedState.currentDuration * 60)) *>
          trackingStatusRef.get.flatMap(status => webSocketService.broadcastTrackingStatus(webSocketTopic, status)) *>
          (if (debug) IO.println(s"[$processName] duration: ${updatedState.totalDuration}, session: ${updatedState.currentDuration}") else IO.unit) *>
          IO.pure((updatedState, ()))
        }
      }
      .interruptWhen(pauseSignal.map(identity))
      .compile
      .lastOrError
      .flatMap { case (finalState, _) =>
        // Handle termination
        for {
          _ <- if (debug) IO.println(s"Process $processName stopped. Session: ${finalState.currentDuration}, Total: ${finalState.totalDuration}") else IO.unit

          // Final database update
          _ <- db.updateDuration(app.id, finalState.totalDuration)

          // Update longest session if needed
          _ <- if (finalState.currentDuration > app.longestSession.getOrElse(0)) {
            db.updateLongestSessionRecord(app.id, finalState.currentDuration) *>
            (if (debug) IO.println(s"New longest session for $processName: ${finalState.currentDuration} minutes") else IO.unit)
          } else IO.unit

          // Update tracking status
          _ <- trackingStatusRef.update(_.copy(currentApp = None, currentSessionDuration = 0))
          status <- trackingStatusRef.get
          _ <- webSocketService.broadcastTrackingStatus(webSocketTopic, status)

          // Send notification
          _ <- ProcessUtils.sendNotification(s"process: \"$processName\" | session: ${finalState.currentDuration} | total: ${finalState.totalDuration}")
        } yield ()
      }
  }
}

// Enhanced Discovery Service
class DiscoveryService(
    db: DatabaseOps,
    processMonitor: ProcessMonitor,
    pauseSignal: SignallingRef[IO, Boolean],
    debug: Boolean
) {

  private val runningProcesses = scala.collection.concurrent.TrieMap[String, Fiber[IO, Throwable, Unit]]()
  private val actionFlags = scala.collection.concurrent.TrieMap[String, Ref[IO, Boolean]]()

  def startDiscovery: IO[Unit] = {
    Stream.awakeEvery[IO](5.second)
      .evalMap { _ =>
        pauseSignal.get.flatMap { isPaused =>
          if (isPaused) {
            if (debug) IO.println("Discovery paused") else IO.unit
          } else {
            discoverProcesses
          }
        }
      }
      .compile
      .drain
  }

  private def discoverProcesses: IO[Unit] = {
    db.getAllApps.flatMap { trackedApps =>
      trackedApps.traverse_ { app =>
        app.name.traverse_ { processName =>
          ProcessUtils.isProcessRunning(processName).flatMap { isRunning =>
            val isCurrentlyTracked = runningProcesses.contains(processName)

            if (isRunning && !isCurrentlyTracked) {
              getOrCreateActionFlag(processName).flatMap { actionFlag =>
                actionFlag.get.flatMap { inProgress =>
                  IO.whenA(!inProgress) {
                    startMonitoring(processName, app, actionFlag)
                  }
                }
              }
            } else if (!isRunning && isCurrentlyTracked) {
              stopMonitoring(processName)
            } else IO.unit
          }
        }
      }
    }
  }

  private def getOrCreateActionFlag(processName: String): IO[Ref[IO, Boolean]] = {
    actionFlags.get(processName) match {
      case Some(flag) => IO.pure(flag)
      case None =>
        Ref[IO].of(false).map { newFlag =>
          actionFlags.put(processName, newFlag)
          newFlag
        }
    }
  }

  private def startMonitoring(processName: String, app: App, actionFlag: Ref[IO, Boolean]): IO[Unit] = {
    for {
      _ <- if (debug) IO.println(s"Starting to monitor: $processName") else IO.unit
      _ <- actionFlag.set(true)
      fiber <- processMonitor.monitorProcess(processName, app)
        .guarantee(actionFlag.set(false))
        .start
      _ = runningProcesses.put(processName, fiber)
    } yield ()
  }

  private def stopMonitoring(processName: String): IO[Unit] = {
    runningProcesses.remove(processName) match {
      case Some(fiber) =>
        if (debug) IO.println(s"Stopping monitor for: $processName") else IO.unit
      case None => IO.unit
    }
  }
}

// Main Application with HTTP Server
object Main extends IOApp {

  private def createSession: Resource[IO, Session[IO]] =
    Session.single[IO](
      host = "********",
      port = 5432,
      user = "postgres",
      database = "time_tracker",
      password = Some("root")
    )

  private def displayApp(db: DatabaseOps, processName: String): IO[ExitCode] =
    db.getAppByName(processName).flatMap {
      case Some(app) =>
        IO.println(s"Time data for app: '$processName'\n") *>
        IO.println(s"ID: ${app.id}") *>
        IO.println(s"Name: ${app.name.getOrElse("N/A")}") *>
        IO.println(s"Product Name: ${app.productName.getOrElse("N/A")}") *>
        IO.println(s"Duration: ${app.duration.map(d => s"$d minutes").getOrElse("N/A")}") *>
        IO.println(s"Launches: ${app.launches.getOrElse("N/A")}") *>
        IO.println(s"Longest Session: ${app.longestSession.map(ls => s"$ls minutes").getOrElse("N/A")}") *>
        IO.println(s"Longest Session On: ${app.longestSessionOn.map(_.toString).getOrElse("N/A")}") *>
        IO.pure(ExitCode.Success)
      case None =>
        IO.println(s"Could not find App: '$processName'") *>
        IO.pure(ExitCode.Error)
    }

  private def displayAllApps(db: DatabaseOps): IO[ExitCode] =
    db.getAllApps.flatMap { apps =>
      IO.println("Time data for all apps.\n") *>
      apps.traverse_ { app =>
        IO.println(s"ID: ${app.id}") *>
        IO.println(s"Name: ${app.name.getOrElse("N/A")}") *>
        IO.println(s"Product Name: ${app.productName.getOrElse("N/A")}") *>
        IO.println(s"Duration: ${app.duration.map(d => s"$d minutes").getOrElse("N/A")}") *>
        IO.println(s"Launches: ${app.launches.getOrElse("N/A")}") *>
        IO.println(s"Longest Session: ${app.longestSession.map(ls => s"$ls minutes").getOrElse("N/A")}") *>
        IO.println(s"Longest Session On: ${app.longestSessionOn.map(_.toString).getOrElse("N/A")}") *>
        IO.println("-" * 30)
      } *> IO.pure(ExitCode.Success)
    }

  private def insertApp(db: DatabaseOps, processName: String, debug: Boolean): IO[ExitCode] =
    db.getAppByName(processName).flatMap {
      case Some(_) =>
        (if (debug) IO.println(s"App '$processName' already exists") else IO.unit) *>
        IO.pure(ExitCode.Success)
      case None =>
        db.insertNewApp(processName) *>
        (if (debug) IO.println(s"App '$processName' has been inserted") else IO.unit) *>
        IO.pure(ExitCode.Success)
    }

  private def sendPauseCommand: IO[ExitCode] = {
    IO {
      Try {
        os.write(os.Path("/tmp/time-tracker-commands"), "pause")
        println("Pause command sent")
      }.recover { case ex =>
        println(s"Failed to send pause command: ${ex.getMessage}")
      }
    } *> IO.pure(ExitCode.Success)
  }

  private def startTrackingService(db: DatabaseOps, debug: Boolean): IO[ExitCode] = {
    for {
      pauseSignal <- SignallingRef[IO, Boolean](false)
      trackingStatusRef <- Ref[IO].of(TrackingStatus(
        isTracking = false,
        isPaused = false,
        currentApp = None,
        currentSessionDuration = 0,
        sessionStartTime = None
      ))

      // Create WebSocket service and topic (for legacy mode, but not used)
      webSocketService = new WebSocketService()
      webSocketTopic <- webSocketService.createWebSocketTopic

      processMonitor = new ProcessMonitor(db, pauseSignal, debug, trackingStatusRef, webSocketService, webSocketTopic)
      communicationService = new EnhancedCommunicationService(pauseSignal, db, debug, trackingStatusRef, webSocketService, webSocketTopic)
      discoveryService = new DiscoveryService(db, processMonitor, pauseSignal, debug)

      _ <- if (debug) IO.println("Starting time tracker service...") else IO.unit

      // Start command listener (legacy file-based)
      _ <- communicationService.startCommandListener.start

      // Start discovery service
      _ <- discoveryService.startDiscovery
    } yield ExitCode.Success
  }

  private def startHttpServer(db: DatabaseOps, debug: Boolean, port: Int): IO[ExitCode] = {
    for {
      pauseSignal <- SignallingRef[IO, Boolean](false)
      trackingStatusRef <- Ref[IO].of(TrackingStatus(
        isTracking = false,
        isPaused = false,
        currentApp = None,
        currentSessionDuration = 0,
        sessionStartTime = None
      ))

      // Create WebSocket service and topic
      webSocketService = new WebSocketService()
      webSocketTopic <- webSocketService.createWebSocketTopic

      processMonitor = new ProcessMonitor(db, pauseSignal, debug, trackingStatusRef, webSocketService, webSocketTopic)
      communicationService = new EnhancedCommunicationService(pauseSignal, db, debug, trackingStatusRef, webSocketService, webSocketTopic)
      discoveryService = new DiscoveryService(db, processMonitor, pauseSignal, debug)
      apiRoutes = new ApiRoutes(db, communicationService, webSocketService, webSocketTopic, debug)

      _ <- if (debug) IO.println(s"Starting HTTP server on port $port...") else IO.unit

      // Start background services
      _ <- communicationService.startCommandListener.start
      _ <- communicationService.startStatusBroadcaster.start
      _ <- discoveryService.startDiscovery.start

      // Start HTTP server
      server <- EmberServerBuilder.default[IO]
        .withHost(ipv4"0.0.0.0")
        .withPort(Port.fromInt(port).getOrElse(Port.fromInt(8080).get))
        .withHttpApp(apiRoutes.routes.orNotFound)
        .build
        .use { server =>
          IO.println(s"Server started at ${server.address}") *>
          IO.println("API endpoints available:") *>
          IO.println("  GET    /api/apps") *>
          IO.println("  GET    /api/apps/{name}") *>
          IO.println("  POST   /api/apps") *>
          IO.println("  DELETE /api/apps/{id}") *>
          IO.println("  GET    /api/tracking/status") *>
          IO.println("  POST   /api/tracking/command") *>
          IO.println("  GET    /api/timeline") *>
          IO.println("  GET    /api/statistics") *>
          IO.println("  WS     /ws (WebSocket for real-time updates)") *>
          IO.println("Press Ctrl+C to stop the server") *>
          IO.never
        }
    } yield ExitCode.Success
  }

  def run(args: List[String]): IO[ExitCode] = {
    CaseApp.parse[Config](args) match {
      case Right((config, remaining)) =>
        createSession.use { session =>
          val db = new DatabaseOps(session)

          config match {
            case Config(_, true, _, _, _, _, _) =>
              displayAllApps(db)
            case Config(Some(processName), _, _, _, _, _, _) =>
              displayApp(db, processName)
            case Config(_, _, Some(processName), debug, _, _, _) =>
              insertApp(db, processName, debug)
            case Config(_, _, _, _, true, _, _) =>
              sendPauseCommand
            case Config(_, _, _, debug, _, true, port) =>
              startHttpServer(db, debug, port)
            case _ =>
              startTrackingService(db, config.debug)
          }
        }
      case Left(error) =>
        IO.println(s"Error parsing arguments: ${error.message}") *>
        IO.pure(ExitCode.Error)
    }
  }
}
