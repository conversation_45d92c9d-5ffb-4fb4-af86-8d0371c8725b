import 'dart:convert';
import 'dart:io';

void main() async {
  print('Testing connection to Scala backend...');
  
  final client = HttpClient();
  
  try {
    // Test 1: Get all apps
    print('\n1. Testing GET /api/apps');
    final request1 = await client.getUrl(Uri.parse('http://localhost:8080/api/apps'));
    final response1 = await request1.close();
    final responseBody1 = await response1.transform(utf8.decoder).join();
    print('Status: ${response1.statusCode}');
    
    if (response1.statusCode == 200) {
      final data = json.decode(responseBody1);
      print('Success: ${data['success']}');
      print('Number of apps: ${(data['data'] as List).length}');
      print('First app: ${(data['data'] as List).isNotEmpty ? (data['data'] as List)[0] : 'None'}');
    } else {
      print('Error: $responseBody1');
    }
    
    // Test 2: Get tracking status
    print('\n2. Testing GET /api/tracking/status');
    final request2 = await client.getUrl(Uri.parse('http://localhost:8080/api/tracking/status'));
    final response2 = await request2.close();
    final responseBody2 = await response2.transform(utf8.decoder).join();
    print('Status: ${response2.statusCode}');
    
    if (response2.statusCode == 200) {
      final data = json.decode(responseBody2);
      print('Success: ${data['success']}');
      print('Tracking status: ${data['data']}');
    } else {
      print('Error: $responseBody2');
    }
    
    // Test 3: Send tracking command
    print('\n3. Testing POST /api/tracking/command');
    final request3 = await client.postUrl(Uri.parse('http://localhost:8080/api/tracking/command'));
    request3.headers.set('Content-Type', 'application/json');
    request3.write(json.encode({'command': 'start'}));
    final response3 = await request3.close();
    final responseBody3 = await response3.transform(utf8.decoder).join();
    print('Status: ${response3.statusCode}');
    
    if (response3.statusCode == 200) {
      final data = json.decode(responseBody3);
      print('Success: ${data['success']}');
      print('Message: ${data['message']}');
    } else {
      print('Error: $responseBody3');
    }
    
    // Test 4: Get statistics
    print('\n4. Testing GET /api/statistics');
    final request4 = await client.getUrl(Uri.parse('http://localhost:8080/api/statistics'));
    final response4 = await request4.close();
    final responseBody4 = await response4.transform(utf8.decoder).join();
    print('Status: ${response4.statusCode}');
    
    if (response4.statusCode == 200) {
      final data = json.decode(responseBody4);
      print('Success: ${data['success']}');
      print('Number of statistics: ${(data['data'] as List).length}');
      if ((data['data'] as List).isNotEmpty) {
        final firstStat = (data['data'] as List)[0];
        print('First app stats: ${firstStat['app']['name']} - Total: ${firstStat['totalDuration']} min');
      }
    } else {
      print('Error: $responseBody4');
    }
    
  } catch (e) {
    print('Connection error: $e');
    print('Make sure the Scala backend is running on http://localhost:8080');
  } finally {
    client.close();
  }
}
